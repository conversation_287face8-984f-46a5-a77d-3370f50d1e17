# Dream Portal Automation

Automated testing for Dream Portal application using Playwright with TypeScript.

## Project Structure

```
DreamPortalAutomation/
├── src/                         # Source code (Page Objects, utilities)
│   ├── pages/                   # Page Object Model (POM) classes
│   │   ├── BasePage.ts          # Base page with common functionality
│   │   ├── HomePage.ts          # Represents index.html
│   │   ├── DiaryPage.ts         # Represents dreams-diary.html
│   │   └── SummaryPage.ts       # Represents dreams-total.html
│   │
│   └── utils/                   # Helper classes, utilities
│       ├── driverSetup.ts       # Browser setup and management
│       ├── testHelpers.ts       # Common test helper functions
│       └── constants.ts         # URLs, selectors, expected data
│
├── tests/                       # Automated Test Cases
│   ├── HomePage.test.ts         # Tests for index.html (Home Page)
│   ├── DiaryPage.test.ts        # Tests for dreams-diary.html
│   └── SummaryPage.test.ts      # Tests for dreams-total.html
│
├── reports/                     # Test execution reports (HTML, JSON)
├── screenshots/                 # Failure screenshots and test evidence
├── playwright.config.ts         # Playwright configuration
├── tsconfig.json               # TypeScript configuration
└── README.md                   # Project documentation
```

## Test Requirements Covered

### 📌 index.html — Home Page
- ✅ Verify loading animation appears, then disappears (~3 sec)
- ✅ Check that "My Dreams" button appears
- ✅ Click "My Dreams" → Assert two new tabs/windows open (Diary + Summary)

### 📌 dreams-diary.html — Diary Table
- ✅ Assert exactly 10 dream entries
- ✅ Assert Dream Types → Either "Good" or "Bad"
- ✅ Each row → 3 columns filled: Dream Name, Days Ago, Dream Type

### 📌 dreams-total.html — Summary Page
- ✅ Good Dreams = 6 | Bad Dreams = 4 | Total = 10 | Recurring = 2
- ✅ Assert recurring dreams: "Flying over mountains", "Lost in maze"

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn

### Installation

1. **Clone/Download the project**
   ```bash
   cd DreamPortalAutomation
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install Playwright browsers**
   ```bash
   npm run install:browsers
   ```

4. **Update URLs in constants.ts**
   
   Edit `src/utils/constants.ts` and update the URLs to point to your actual Dream Portal files:
   ```typescript
   export const URLS = {
     HOME: 'file:///C:/path/to/your/index.html',
     DIARY: 'file:///C:/path/to/your/dreams-diary.html',
     SUMMARY: 'file:///C:/path/to/your/dreams-total.html'
   } as const;
   ```

## Running Tests

### Run All Tests
```bash
npm test
```

### Run Tests in Headed Mode (Visible Browser)
```bash
npm run test:headed
```

### Run Specific Test Files
```bash
npm run test:home      # Home page tests only
npm run test:diary     # Diary page tests only
npm run test:summary   # Summary page tests only
```

### Debug Mode
```bash
npm run test:debug
```

### Interactive UI Mode
```bash
npm run test:ui
```

## Test Reports

### View HTML Report
```bash
npm run report
```

Reports are generated in the `reports/` directory:
- `reports/html-report/` - Interactive HTML report
- `reports/test-results.json` - JSON test results

## Screenshots

Screenshots are automatically captured:
- On test failures (saved to `screenshots/`)
- During test execution for verification
- Full page screenshots with timestamps

## Configuration

### Browser Configuration
Tests run on multiple browsers by default:
- Chromium (Chrome)
- Firefox
- WebKit (Safari)

### Timeouts
Configured in `src/utils/constants.ts`:
- Loading animation: 3 seconds
- Page load: 10 seconds
- Element wait: 5 seconds
- Navigation: 8 seconds

### Selectors
Multiple selector strategies for robustness:
- Data test IDs (preferred)
- CSS selectors
- Text-based selectors

## Troubleshooting

### Common Issues

1. **Tests fail with "Element not found"**
   - Update selectors in `src/utils/constants.ts`
   - Check if your HTML structure matches expected selectors

2. **URLs not working**
   - Update file paths in `src/utils/constants.ts`
   - Ensure file:// URLs are correct for your system

3. **Loading animation tests fail**
   - Animation might be too fast to detect
   - Adjust timeouts in constants.ts if needed

4. **New tabs not opening**
   - Check if your "My Dreams" button actually opens new windows
   - Verify JavaScript functionality in your HTML files

### Debug Tips

1. **Run in headed mode** to see what's happening:
   ```bash
   npm run test:headed
   ```

2. **Use debug mode** to step through tests:
   ```bash
   npm run test:debug
   ```

3. **Check screenshots** in the `screenshots/` folder

4. **View detailed reports** with:
   ```bash
   npm run report
   ```

## Extending Tests

### Adding New Tests
1. Create new test files in `tests/` directory
2. Follow existing patterns and use Page Object Model
3. Import required page objects and utilities

### Adding New Page Objects
1. Create new page class in `src/pages/`
2. Extend `BasePage` class
3. Add selectors to `constants.ts`

### Customizing Selectors
Update `src/utils/constants.ts` with your specific selectors:
```typescript
export const SELECTORS = {
  HOME: {
    LOADING_ANIMATION: 'your-loading-selector',
    MY_DREAMS_BUTTON: 'your-button-selector'
  }
  // ... more selectors
};
```

## Contributing

1. Follow TypeScript best practices
2. Use Page Object Model pattern
3. Add appropriate test annotations
4. Include error handling and logging
5. Update documentation for new features
