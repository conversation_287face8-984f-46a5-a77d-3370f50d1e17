/**
 * Example Constants Configuration for Dream Portal Test Automation
 * 
 * Copy this file to constants.ts and update the URLs to match your actual file paths
 */

// URLs - UPDATE THESE WITH YOUR ACTUAL DREAM PORTAL FILE PATHS
export const URLS = {
  // Example for Windows file paths:
  HOME: 'file:///C:/Users/<USER>/Desktop/DreamPortal/index.html',
  DIARY: 'file:///C:/Users/<USER>/Desktop/DreamPortal/dreams-diary.html',
  SUMMARY: 'file:///C:/Users/<USER>/Desktop/DreamPortal/dreams-total.html'
  
  // Example for Mac/Linux file paths:
  // HOME: 'file:///Users/<USER>/Desktop/DreamPortal/index.html',
  // DIARY: 'file:///Users/<USER>/Desktop/DreamPortal/dreams-diary.html',
  // SUMMARY: 'file:///Users/<USER>/Desktop/DreamPortal/dreams-total.html'
  
  // Example for web server URLs:
  // HOME: 'http://localhost:3000/index.html',
  // DIARY: 'http://localhost:3000/dreams-diary.html',
  // SUMMARY: 'http://localhost:3000/dreams-total.html'
} as const;

// Timeouts - Adjust these based on your application's performance
export const TIMEOUTS = {
  LOADING_ANIMATION: 3000, // 3 seconds for loading animation
  PAGE_LOAD: 10000, // 10 seconds for page load
  ELEMENT_WAIT: 5000, // 5 seconds for element to appear
  NAVIGATION: 8000 // 8 seconds for navigation between pages
} as const;

// Expected Data - Update these if your test data is different
export const EXPECTED_DATA = {
  DIARY: {
    TOTAL_ENTRIES: 10,
    DREAM_TYPES: ['Good', 'Bad'] as const,
    COLUMNS: ['Dream Name', 'Days Ago', 'Dream Type'] as const
  },
  SUMMARY: {
    GOOD_DREAMS: 6,
    BAD_DREAMS: 4,
    TOTAL_DREAMS: 10,
    RECURRING_DREAMS: 2,
    RECURRING_DREAM_NAMES: [
      'Flying over mountains',
      'Lost in maze'
    ] as const
  }
} as const;

// Selectors - Update these to match your HTML structure
export const SELECTORS = {
  HOME: {
    // Multiple selector strategies for robustness
    // Add your specific selectors here, separated by commas
    LOADING_ANIMATION: '[data-testid="loading-animation"], .loading, .spinner, #loading',
    MY_DREAMS_BUTTON: '[data-testid="my-dreams-button"], button:has-text("My Dreams"), #myDreamsBtn, .my-dreams-btn'
  },
  DIARY: {
    TABLE: '[data-testid="dreams-table"], table, .dreams-table, #dreamsTable',
    TABLE_ROWS: '[data-testid="dream-row"], tbody tr, .dream-entry, .dream-row',
    DREAM_NAME_COLUMN: '[data-testid="dream-name"], td:nth-child(1), .dream-name',
    DAYS_AGO_COLUMN: '[data-testid="days-ago"], td:nth-child(2), .days-ago',
    DREAM_TYPE_COLUMN: '[data-testid="dream-type"], td:nth-child(3), .dream-type'
  },
  SUMMARY: {
    GOOD_DREAMS_COUNT: '[data-testid="good-dreams"], .good-dreams, #goodDreams, .good-count',
    BAD_DREAMS_COUNT: '[data-testid="bad-dreams"], .bad-dreams, #badDreams, .bad-count',
    TOTAL_DREAMS_COUNT: '[data-testid="total-dreams"], .total-dreams, #totalDreams, .total-count',
    RECURRING_DREAMS_COUNT: '[data-testid="recurring-dreams"], .recurring-dreams, #recurringDreams, .recurring-count',
    RECURRING_DREAMS_LIST: '[data-testid="recurring-list"], .recurring-list, #recurringList, .recurring-dreams-list li'
  }
} as const;

// Test Data
export const TEST_DATA = {
  BROWSER_CONTEXTS: {
    VIEWPORT: { width: 1280, height: 720 },
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  }
} as const;

/*
SELECTOR CUSTOMIZATION GUIDE:

1. Data Test IDs (Recommended):
   Add data-testid attributes to your HTML elements:
   <button data-testid="my-dreams-button">My Dreams</button>

2. CSS Classes:
   Use specific CSS classes:
   <div class="loading-animation">...</div>

3. IDs:
   Use unique IDs:
   <table id="dreamsTable">...</table>

4. Text-based selectors:
   For buttons with specific text:
   button:has-text("My Dreams")

5. CSS Selectors:
   Standard CSS selectors:
   tbody tr (for table rows)
   td:nth-child(1) (for first column)

TIPS:
- List selectors in order of preference (most specific first)
- Separate multiple selectors with commas
- The test framework will try each selector until one works
- Use data-testid attributes for the most reliable tests
*/
