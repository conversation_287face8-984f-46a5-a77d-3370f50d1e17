import { test, expect } from '@playwright/test';
import { SummaryPage } from '../src/pages/SummaryPage';
import { EXPECTED_DATA } from '../src/utils/constants';

test.describe('Summary Page Tests - dreams-total.html', () => {
  let summaryPage: SummaryPage;

  test.beforeEach(async ({ page }) => {
    summaryPage = new SummaryPage(page);
  });

  test('should display correct dream counts', async ({ page }) => {
    test.info().annotations.push({
      type: 'requirement',
      description: 'Good Dreams = 6 | Bad Dreams = 4 | Total = 10 | Recurring = 2'
    });

    // Navigate to summary page
    await summaryPage.navigate();

    // Verify all counts
    await summaryPage.verifyGoodDreamsCount();
    await summaryPage.verifyBadDreamsCount();
    await summaryPage.verifyTotalDreamsCount();
    await summaryPage.verifyRecurringDreamsCount();

    // Get actual values for reporting
    const stats = await summaryPage.getAllSummaryStats();
    expect(stats.goodDreams).toBe(6);
    expect(stats.badDreams).toBe(4);
    expect(stats.totalDreams).toBe(10);
    expect(stats.recurringDreams).toBe(2);

    // Log summary for debugging
    console.log('Summary stats:', stats);

    // Take screenshot for verification
    await summaryPage.takeScreenshot('summary-page-counts');
  });

  test('should display specific recurring dreams', async ({ page }) => {
    test.info().annotations.push({
      type: 'requirement',
      description: 'Assert recurring dreams: "Flying over mountains", "Lost in maze"'
    });

    // Navigate to summary page
    await summaryPage.navigate();

    // Verify recurring dreams list
    await summaryPage.verifyRecurringDreamsList();

    // Get actual recurring dreams for detailed verification
    const recurringDreams = await summaryPage.getRecurringDreamsList();
    
    // Verify we have at least 2 recurring dreams
    expect(recurringDreams.length).toBeGreaterThanOrEqual(2);

    // Verify specific dreams are present
    const expectedDreams = EXPECTED_DATA.SUMMARY.RECURRING_DREAM_NAMES;
    
    for (const expectedDream of expectedDreams) {
      const found = recurringDreams.some(dream => 
        dream.toLowerCase().includes(expectedDream.toLowerCase())
      );
      expect(found).toBe(true);
    }

    // Log recurring dreams for debugging
    console.log('Recurring dreams found:', recurringDreams);
    console.log('Expected dreams:', expectedDreams);

    // Take screenshot showing recurring dreams
    await summaryPage.takeScreenshot('summary-page-recurring-dreams');
  });

  test('should maintain mathematical consistency', async ({ page }) => {
    test.info().annotations.push({
      type: 'validation',
      description: 'Verify Good Dreams + Bad Dreams = Total Dreams'
    });

    // Navigate to summary page
    await summaryPage.navigate();

    // Verify mathematical consistency
    await summaryPage.verifyMathematicalConsistency();

    // Get individual counts for detailed verification
    const goodDreams = await summaryPage.getGoodDreamsCount();
    const badDreams = await summaryPage.getBadDreamsCount();
    const totalDreams = await summaryPage.getTotalDreamsCount();

    // Verify the math
    expect(goodDreams + badDreams).toBe(totalDreams);
    expect(goodDreams + badDreams).toBe(10);

    // Log for debugging
    console.log(`Mathematical verification: ${goodDreams} + ${badDreams} = ${totalDreams}`);
  });

  test('should display all summary statistics correctly', async ({ page }) => {
    test.info().annotations.push({
      type: 'comprehensive',
      description: 'Verify all summary statistics are displayed and accessible'
    });

    // Navigate to summary page
    await summaryPage.navigate();

    // Get all statistics
    const stats = await summaryPage.getAllSummaryStats();

    // Verify all statistics are present and correct
    expect(stats.goodDreams).toBe(EXPECTED_DATA.SUMMARY.GOOD_DREAMS);
    expect(stats.badDreams).toBe(EXPECTED_DATA.SUMMARY.BAD_DREAMS);
    expect(stats.totalDreams).toBe(EXPECTED_DATA.SUMMARY.TOTAL_DREAMS);
    expect(stats.recurringDreams).toBe(EXPECTED_DATA.SUMMARY.RECURRING_DREAMS);
    expect(stats.recurringDreamsList.length).toBeGreaterThanOrEqual(EXPECTED_DATA.SUMMARY.RECURRING_DREAMS);

    // Verify recurring dreams contain expected items
    for (const expectedDream of EXPECTED_DATA.SUMMARY.RECURRING_DREAM_NAMES) {
      const found = stats.recurringDreamsList.some(dream => 
        dream.toLowerCase().includes(expectedDream.toLowerCase())
      );
      expect(found).toBe(true);
    }

    // Get summary text for reporting
    const summaryText = await summaryPage.getSummaryText();
    console.log('Summary text:', summaryText);
  });

  test('should complete full summary page verification', async ({ page }) => {
    test.info().annotations.push({
      type: 'comprehensive',
      description: 'Complete verification of all summary page requirements'
    });

    // Navigate to summary page
    await summaryPage.navigate();

    // Run complete verification
    await summaryPage.verifySummaryPageComplete();

    // Additional verification - check page is properly loaded
    const isLoaded = await summaryPage.isPageLoaded();
    expect(isLoaded).toBe(true);

    // Take final screenshot
    await summaryPage.takeScreenshot('summary-page-complete-verification');
  });

  test('should handle individual count retrievals', async ({ page }) => {
    test.info().annotations.push({
      type: 'functionality',
      description: 'Verify individual count retrieval methods work correctly'
    });

    // Navigate to summary page
    await summaryPage.navigate();

    // Test individual count methods
    const goodDreams = await summaryPage.getGoodDreamsCount();
    const badDreams = await summaryPage.getBadDreamsCount();
    const totalDreams = await summaryPage.getTotalDreamsCount();
    const recurringDreams = await summaryPage.getRecurringDreamsCount();

    // Verify all counts are numbers and within expected ranges
    expect(typeof goodDreams).toBe('number');
    expect(typeof badDreams).toBe('number');
    expect(typeof totalDreams).toBe('number');
    expect(typeof recurringDreams).toBe('number');

    expect(goodDreams).toBeGreaterThan(0);
    expect(badDreams).toBeGreaterThan(0);
    expect(totalDreams).toBeGreaterThan(0);
    expect(recurringDreams).toBeGreaterThanOrEqual(0);

    // Verify specific expected values
    expect(goodDreams).toBe(6);
    expect(badDreams).toBe(4);
    expect(totalDreams).toBe(10);
    expect(recurringDreams).toBe(2);
  });

  test('should handle page reload correctly', async ({ page }) => {
    test.info().annotations.push({
      type: 'robustness',
      description: 'Verify summary page works correctly after reload'
    });

    // Navigate to summary page
    await summaryPage.navigate();

    // Verify initial state
    await summaryPage.verifyGoodDreamsCount();

    // Reload the page
    await page.reload();

    // Verify everything still works after reload
    await summaryPage.verifySummaryPageComplete();

    // Verify specific counts are still correct
    const stats = await summaryPage.getAllSummaryStats();
    expect(stats.goodDreams).toBe(6);
    expect(stats.badDreams).toBe(4);
    expect(stats.totalDreams).toBe(10);
    expect(stats.recurringDreams).toBe(2);
  });
});
