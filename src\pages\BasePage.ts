import { Page } from '@playwright/test';
import { TestHelpers } from '../utils/testHelpers';
import { TIMEOUTS } from '../utils/constants';

/**
 * Base Page class with common functionality
 */
export abstract class BasePage {
  protected helpers: TestHelpers;

  constructor(protected page: Page) {
    this.helpers = new TestHelpers(page);
  }

  /**
   * Navigate to the page
   */
  abstract navigate(): Promise<void>;

  /**
   * Wait for page to be loaded
   */
  async waitForPageLoad(): Promise<void> {
    await this.helpers.waitForPageLoad();
  }

  /**
   * Get page title
   */
  async getTitle(): Promise<string> {
    return await this.page.title();
  }

  /**
   * Get current URL
   */
  getCurrentUrl(): string {
    return this.page.url();
  }

  /**
   * Take screenshot
   */
  async takeScreenshot(name: string): Promise<void> {
    await this.helpers.takeScreenshot(name);
  }

  /**
   * Wait for element to be visible
   */
  async waitForElement(selector: string, timeout?: number): Promise<void> {
    await this.helpers.waitForElement(selector, timeout);
  }

  /**
   * Check if page is loaded by verifying key elements
   */
  abstract isPageLoaded(): Promise<boolean>;
}
