import { Page, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { URLS, SELECTORS, EXPECTED_DATA } from '../utils/constants';

/**
 * Page Object Model for dreams-diary.html (Diary Page)
 */
export class DiaryPage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Navigate to diary page
   */
  async navigate(): Promise<void> {
    await this.page.goto(URLS.DIARY);
    await this.waitForPageLoad();
  }

  /**
   * Get the dreams table element
   */
  async getDreamsTable() {
    return await this.helpers.getElementByMultipleSelectors(SELECTORS.DIARY.TABLE);
  }

  /**
   * Get all dream entry rows
   */
  async getDreamRows() {
    return await this.helpers.getElementByMultipleSelectors(SELECTORS.DIARY.TABLE_ROWS);
  }

  /**
   * Count total dream entries
   */
  async getDreamEntriesCount(): Promise<number> {
    return await this.helpers.countElements(SELECTORS.DIARY.TABLE_ROWS);
  }

  /**
   * Verify exactly 10 dream entries exist
   */
  async verifyDreamEntriesCount(): Promise<void> {
    const count = await this.getDreamEntriesCount();
    expect(count).toBe(EXPECTED_DATA.DIARY.TOTAL_ENTRIES);
  }

  /**
   * Get all dream names from the table
   */
  async getAllDreamNames(): Promise<string[]> {
    return await this.helpers.getAllTextContents(SELECTORS.DIARY.DREAM_NAME_COLUMN);
  }

  /**
   * Get all days ago values from the table
   */
  async getAllDaysAgo(): Promise<string[]> {
    return await this.helpers.getAllTextContents(SELECTORS.DIARY.DAYS_AGO_COLUMN);
  }

  /**
   * Get all dream types from the table
   */
  async getAllDreamTypes(): Promise<string[]> {
    return await this.helpers.getAllTextContents(SELECTORS.DIARY.DREAM_TYPE_COLUMN);
  }

  /**
   * Verify all dream types are either "Good" or "Bad"
   */
  async verifyDreamTypes(): Promise<void> {
    const dreamTypes = await this.getAllDreamTypes();
    
    // Check that we have dream types
    expect(dreamTypes.length).toBeGreaterThan(0);
    
    // Verify each dream type is either "Good" or "Bad"
    for (const dreamType of dreamTypes) {
      expect(EXPECTED_DATA.DIARY.DREAM_TYPES).toContain(dreamType as any);
    }
  }

  /**
   * Verify each row has all 3 columns filled
   */
  async verifyAllColumnsAreFilled(): Promise<void> {
    const dreamNames = await this.getAllDreamNames();
    const daysAgo = await this.getAllDaysAgo();
    const dreamTypes = await this.getAllDreamTypes();

    // All arrays should have the same length
    expect(dreamNames.length).toBe(EXPECTED_DATA.DIARY.TOTAL_ENTRIES);
    expect(daysAgo.length).toBe(EXPECTED_DATA.DIARY.TOTAL_ENTRIES);
    expect(dreamTypes.length).toBe(EXPECTED_DATA.DIARY.TOTAL_ENTRIES);

    // Verify no empty values
    for (let i = 0; i < EXPECTED_DATA.DIARY.TOTAL_ENTRIES; i++) {
      expect(dreamNames[i]).toBeTruthy();
      expect(daysAgo[i]).toBeTruthy();
      expect(dreamTypes[i]).toBeTruthy();
    }
  }

  /**
   * Get dream entry by index (0-based)
   */
  async getDreamEntryByIndex(index: number): Promise<{
    name: string;
    daysAgo: string;
    type: string;
  }> {
    const dreamNames = await this.getAllDreamNames();
    const daysAgo = await this.getAllDaysAgo();
    const dreamTypes = await this.getAllDreamTypes();

    if (index >= dreamNames.length) {
      throw new Error(`Dream entry index ${index} is out of bounds. Total entries: ${dreamNames.length}`);
    }

    return {
      name: dreamNames[index],
      daysAgo: daysAgo[index],
      type: dreamTypes[index]
    };
  }

  /**
   * Verify table structure and headers
   */
  async verifyTableStructure(): Promise<void> {
    // Verify table exists
    const table = await this.getDreamsTable();
    expect(await table.isVisible()).toBe(true);

    // Verify we have the expected number of rows
    await this.verifyDreamEntriesCount();
  }

  /**
   * Get all dream entries as objects
   */
  async getAllDreamEntries(): Promise<Array<{
    name: string;
    daysAgo: string;
    type: string;
  }>> {
    const dreamNames = await this.getAllDreamNames();
    const daysAgo = await this.getAllDaysAgo();
    const dreamTypes = await this.getAllDreamTypes();

    const entries = [];
    for (let i = 0; i < dreamNames.length; i++) {
      entries.push({
        name: dreamNames[i],
        daysAgo: daysAgo[i],
        type: dreamTypes[i]
      });
    }

    return entries;
  }

  /**
   * Check if page is loaded
   */
  async isPageLoaded(): Promise<boolean> {
    try {
      const table = await this.helpers.getElementByMultipleSelectors(SELECTORS.DIARY.TABLE);
      return await table.isVisible();
    } catch (error) {
      return false;
    }
  }

  /**
   * Verify complete diary page functionality
   */
  async verifyDiaryPageComplete(): Promise<void> {
    // Verify table structure
    await this.verifyTableStructure();
    
    // Verify exactly 10 entries
    await this.verifyDreamEntriesCount();
    
    // Verify dream types are valid
    await this.verifyDreamTypes();
    
    // Verify all columns are filled
    await this.verifyAllColumnsAreFilled();
  }
}
