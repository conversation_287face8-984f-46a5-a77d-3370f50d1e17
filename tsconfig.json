{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@/*": ["./src/*"], "@tests/*": ["./tests/*"]}}, "include": ["src/**/*", "tests/**/*", "playwright.config.ts"], "exclude": ["node_modules", "dist", "reports"]}