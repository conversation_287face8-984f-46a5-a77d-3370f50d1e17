# Quick Setup Guide

## Step 1: Update URLs

1. Copy the example constants file:
   ```bash
   cp src/utils/constants.example.ts src/utils/constants.ts
   ```

2. Edit `src/utils/constants.ts` and update the URLs to point to your Dream Portal files:
   ```typescript
   export const URLS = {
     HOME: 'file:///C:/path/to/your/index.html',
     DIARY: 'file:///C:/path/to/your/dreams-diary.html',
     SUMMARY: 'file:///C:/path/to/your/dreams-total.html'
   } as const;
   ```

## Step 2: Install Dependencies

```bash
npm install
npm run install:browsers
```

## Step 3: Run Tests

```bash
# Run all tests
npm test

# Run in visible browser mode
npm run test:headed

# Run specific tests
npm run test:home
npm run test:diary
npm run test:summary
```

## Step 4: View Results

```bash
# View HTML report
npm run report
```

## Troubleshooting

If tests fail:

1. **Check URLs**: Make sure file paths in `constants.ts` are correct
2. **Check selectors**: Update selectors in `constants.ts` to match your HTML
3. **Run in headed mode**: Use `npm run test:headed` to see what's happening
4. **Check screenshots**: Look in the `screenshots/` folder for failure evidence

## Customizing for Your HTML

Update the selectors in `src/utils/constants.ts`:

```typescript
export const SELECTORS = {
  HOME: {
    LOADING_ANIMATION: 'your-loading-selector',
    MY_DREAMS_BUTTON: 'your-button-selector'
  },
  // ... more selectors
};
```

## Expected HTML Structure

The tests expect:

### Home Page (index.html)
- Loading animation element (optional)
- "My Dreams" button that opens 2 new windows/tabs

### Diary Page (dreams-diary.html)
- Table with exactly 10 rows
- 3 columns: Dream Name, Days Ago, Dream Type
- Dream types should be "Good" or "Bad"

### Summary Page (dreams-total.html)
- Good Dreams count: 6
- Bad Dreams count: 4
- Total Dreams count: 10
- Recurring Dreams count: 2
- List containing "Flying over mountains" and "Lost in maze"
