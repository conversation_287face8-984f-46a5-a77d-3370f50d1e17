<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dream Portal - Home</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .loading { font-size: 24px; color: #666; }
        .my-dreams-btn { 
            font-size: 18px; 
            padding: 15px 30px; 
            background: #4CAF50; 
            color: white; 
            border: none; 
            border-radius: 5px; 
            cursor: pointer;
            display: none;
        }
        .my-dreams-btn:hover { background: #45a049; }
    </style>
</head>
<body>
    <h1>Dream Portal</h1>
    
    <div class="loading" id="loading">Loading your dreams...</div>
    
    <button class="my-dreams-btn" id="myDreamsBtn" onclick="openDreamPages()">My Dreams</button>

    <script>
        // Simulate loading animation
        setTimeout(function() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('myDreamsBtn').style.display = 'inline-block';
        }, 3000);

        function openDreamPages() {
            // Open diary page
            window.open('dreams-diary.html', '_blank');
            // Open summary page
            window.open('dreams-total.html', '_blank');
        }
    </script>
</body>
</html>
