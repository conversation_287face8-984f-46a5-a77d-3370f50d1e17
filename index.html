<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dream Portal - Home</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            overflow: hidden;
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 60px 40px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 90%;
        }

        h1 {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { text-shadow: 0 0 20px rgba(255, 255, 255, 0.5); }
            to { text-shadow: 0 0 30px rgba(255, 255, 255, 0.8); }
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.9;
            font-weight: 300;
        }

        .loading {
            font-size: 1.5rem;
            color: #fff;
            margin: 30px 0;
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.05); }
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .my-dreams-btn {
            font-size: 1.3rem;
            font-weight: 600;
            padding: 18px 40px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            display: none;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .my-dreams-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(238, 90, 36, 0.6);
            background: linear-gradient(45deg, #ee5a24, #ff6b6b);
        }

        .my-dreams-btn:active {
            transform: translateY(-1px);
        }

        .my-dreams-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .my-dreams-btn:hover::before {
            left: 100%;
        }

        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .star {
            position: absolute;
            width: 2px;
            height: 2px;
            background: white;
            border-radius: 50%;
            animation: twinkle 3s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 1s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .dream-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <!-- Animated stars background -->
    <div class="stars" id="stars"></div>

    <div class="container">
        <div class="dream-icon">🌙</div>
        <h1>Dream Portal</h1>
        <p class="subtitle">Enter the realm of your subconscious</p>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>Loading your dreams...</p>
        </div>

        <button class="my-dreams-btn fade-in" id="myDreamsBtn" onclick="openDreamPages()">
            ✨ Explore My Dreams ✨
        </button>
    </div>

    <script>
        // Create animated stars
        function createStars() {
            const starsContainer = document.getElementById('stars');
            const numberOfStars = 100;

            for (let i = 0; i < numberOfStars; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.animationDelay = Math.random() * 3 + 's';
                star.style.animationDuration = (Math.random() * 3 + 2) + 's';
                starsContainer.appendChild(star);
            }
        }

        // Simulate loading animation with enhanced effects
        setTimeout(function() {
            const loading = document.getElementById('loading');
            const button = document.getElementById('myDreamsBtn');

            // Fade out loading
            loading.style.transition = 'opacity 0.5s ease-out';
            loading.style.opacity = '0';

            setTimeout(() => {
                loading.style.display = 'none';
                button.style.display = 'inline-block';
                button.style.animation = 'fadeIn 0.8s ease-out';
            }, 500);
        }, 3000);

        function openDreamPages() {
            // Add click effect
            const button = document.getElementById('myDreamsBtn');
            button.style.transform = 'scale(0.95)';

            setTimeout(() => {
                button.style.transform = '';
                // Open diary page
                window.open('dreams-diary.html', '_blank');
                // Open summary page
                setTimeout(() => {
                    window.open('dreams-total.html', '_blank');
                }, 300);
            }, 150);
        }

        // Initialize stars when page loads
        window.addEventListener('load', createStars);
    </script>
</body>
</html>
