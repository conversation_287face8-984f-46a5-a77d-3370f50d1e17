/**
 * Constants for Dream Portal Test Automation
 */

// URLs - Update these with your actual Dream Portal URLs
export const URLS = {
  HOME: 'file:///C:/Users/<USER>/Desktop/QA/index.html', // Update with actual path
  DIARY: 'file:///C:/Users/<USER>/Desktop/QA/dreams-diary.html', // Update with actual path
  SUMMARY: 'file:///C:/Users/<USER>/Desktop/QA/dreams-total.html' // Update with actual path
} as const;

// Timeouts
export const TIMEOUTS = {
  LOADING_ANIMATION: 3000, // 3 seconds for loading animation
  PAGE_LOAD: 10000, // 10 seconds for page load
  ELEMENT_WAIT: 5000, // 5 seconds for element to appear
  NAVIGATION: 8000 // 8 seconds for navigation between pages
} as const;

// Expected Data
export const EXPECTED_DATA = {
  DIARY: {
    TOTAL_ENTRIES: 10,
    DREAM_TYPES: ['Good', 'Bad'] as const,
    COLUMNS: ['Dream Name', 'Days Ago', 'Dream Type'] as const
  },
  SUMMARY: {
    GOOD_DREAMS: 6,
    BAD_DREAMS: 4,
    TOTAL_DREAMS: 10,
    RECURRING_DREAMS: 2,
    RECURRING_DREAM_NAMES: [
      'Flying over mountains',
      'Lost in maze'
    ] as const
  }
} as const;

// Selectors
export const SELECTORS = {
  HOME: {
    LOADING_ANIMATION: '[data-testid="loading-animation"], .loading, .spinner',
    MY_DREAMS_BUTTON: '[data-testid="my-dreams-button"], button:has-text("My Dreams"), #myDreamsBtn'
  },
  DIARY: {
    TABLE: '[data-testid="dreams-table"], table, .dreams-table',
    TABLE_ROWS: '[data-testid="dream-row"], tbody tr, .dream-entry',
    DREAM_NAME_COLUMN: '[data-testid="dream-name"], td:nth-child(1), .dream-name',
    DAYS_AGO_COLUMN: '[data-testid="days-ago"], td:nth-child(2), .days-ago',
    DREAM_TYPE_COLUMN: '[data-testid="dream-type"], td:nth-child(3), .dream-type'
  },
  SUMMARY: {
    GOOD_DREAMS_COUNT: '[data-testid="good-dreams"], .good-dreams, #goodDreams',
    BAD_DREAMS_COUNT: '[data-testid="bad-dreams"], .bad-dreams, #badDreams',
    TOTAL_DREAMS_COUNT: '[data-testid="total-dreams"], .total-dreams, #totalDreams',
    RECURRING_DREAMS_COUNT: '[data-testid="recurring-dreams"], .recurring-dreams, #recurringDreams',
    RECURRING_DREAMS_LIST: '[data-testid="recurring-list"] li, .recurring-list li, #recurringList li'
  }
} as const;

// Test Data
export const TEST_DATA = {
  BROWSER_CONTEXTS: {
    VIEWPORT: { width: 1280, height: 720 },
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
  }
} as const;
