{"name": "dream-portal-automation", "version": "1.0.0", "description": "Automated testing for Dream Portal application using Playwright", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:home": "playwright test tests/HomePage.test.ts", "test:diary": "playwright test tests/DiaryPage.test.ts", "test:summary": "playwright test tests/SummaryPage.test.ts", "report": "playwright show-report reports/html-report", "install:browsers": "playwright install"}, "keywords": ["playwright", "automation", "testing", "typescript"], "author": "", "license": "ISC", "devDependencies": {"@playwright/test": "^1.53.0", "@types/node": "^24.0.3", "typescript": "^5.8.3"}}