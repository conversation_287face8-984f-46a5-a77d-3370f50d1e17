import { Page, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { URLS, SELECTORS, TIMEOUTS } from '../utils/constants';

/**
 * Page Object Model for index.html (Home Page)
 */
export class HomePage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Navigate to home page
   */
  async navigate(): Promise<void> {
    await this.page.goto(URLS.HOME);
    await this.waitForPageLoad();
  }

  /**
   * Check if loading animation is visible
   */
  async isLoadingAnimationVisible(): Promise<boolean> {
    try {
      const loadingElement = await this.helpers.getElementByMultipleSelectors(
        SELECTORS.HOME.LOADING_ANIMATION
      );
      return await loadingElement.isVisible();
    } catch (error) {
      return false;
    }
  }

  /**
   * Wait for loading animation to appear
   */
  async waitForLoadingAnimationToAppear(): Promise<void> {
    try {
      await this.helpers.waitForElement(SELECTORS.HOME.LOADING_ANIMATION, 2000);
    } catch (error) {
      // Loading animation might be very fast, so we don't fail the test
      console.log('Loading animation not detected or appeared very briefly');
    }
  }

  /**
   * Wait for loading animation to disappear
   */
  async waitForLoadingAnimationToDisappear(): Promise<void> {
    try {
      // First check if animation exists
      const hasAnimation = await this.isLoadingAnimationVisible();
      if (hasAnimation) {
        await this.helpers.waitForElementToDisappear(
          SELECTORS.HOME.LOADING_ANIMATION, 
          TIMEOUTS.LOADING_ANIMATION + 1000
        );
      }
    } catch (error) {
      // Animation might have already disappeared
      console.log('Loading animation already disappeared or not found');
    }
  }

  /**
   * Check if "My Dreams" button is visible
   */
  async isMyDreamsButtonVisible(): Promise<boolean> {
    try {
      const button = await this.helpers.getElementByMultipleSelectors(
        SELECTORS.HOME.MY_DREAMS_BUTTON
      );
      return await button.isVisible();
    } catch (error) {
      return false;
    }
  }

  /**
   * Click "My Dreams" button
   */
  async clickMyDreamsButton(): Promise<void> {
    await this.helpers.clickWithRetry(SELECTORS.HOME.MY_DREAMS_BUTTON);
  }

  /**
   * Wait for "My Dreams" button to appear
   */
  async waitForMyDreamsButton(): Promise<void> {
    await this.helpers.waitForElement(SELECTORS.HOME.MY_DREAMS_BUTTON);
  }

  /**
   * Verify loading sequence: animation appears, then disappears, then button appears
   */
  async verifyLoadingSequence(): Promise<void> {
    // Wait for loading animation to appear (if it exists)
    await this.waitForLoadingAnimationToAppear();
    
    // Wait for loading animation to disappear
    await this.waitForLoadingAnimationToDisappear();
    
    // Verify "My Dreams" button appears
    await this.waitForMyDreamsButton();
    
    // Verify button is visible
    const isButtonVisible = await this.isMyDreamsButtonVisible();
    expect(isButtonVisible).toBe(true);
  }

  /**
   * Click My Dreams button and wait for new pages to open
   */
  async clickMyDreamsAndWaitForNewPages(): Promise<Page[]> {
    const initialPageCount = this.page.context().pages().length;
    
    // Click the button
    await this.clickMyDreamsButton();
    
    // Wait for new pages to open (expecting 2 new pages: Diary + Summary)
    const expectedPageCount = initialPageCount + 2;
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        const currentPages = this.page.context().pages();
        reject(new Error(
          `Timeout waiting for new pages. Expected: ${expectedPageCount}, Current: ${currentPages.length}`
        ));
      }, TIMEOUTS.NAVIGATION);

      const checkPages = () => {
        const currentPages = this.page.context().pages();
        if (currentPages.length >= expectedPageCount) {
          clearTimeout(timeout);
          resolve(currentPages);
        } else {
          setTimeout(checkPages, 100);
        }
      };

      checkPages();
    });
  }

  /**
   * Check if page is loaded
   */
  async isPageLoaded(): Promise<boolean> {
    try {
      // Check if either loading animation or My Dreams button is present
      const hasLoadingAnimation = await this.isLoadingAnimationVisible();
      const hasMyDreamsButton = await this.isMyDreamsButtonVisible();
      
      return hasLoadingAnimation || hasMyDreamsButton;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get the text of My Dreams button
   */
  async getMyDreamsButtonText(): Promise<string> {
    return await this.helpers.getTextContent(SELECTORS.HOME.MY_DREAMS_BUTTON);
  }
}
