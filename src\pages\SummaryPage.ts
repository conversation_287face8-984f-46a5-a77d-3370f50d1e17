import { Page, expect } from '@playwright/test';
import { BasePage } from './BasePage';
import { URLS, SELECTORS, EXPECTED_DATA } from '../utils/constants';

/**
 * Page Object Model for dreams-total.html (Summary Page)
 */
export class SummaryPage extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Navigate to summary page
   */
  async navigate(): Promise<void> {
    await this.page.goto(URLS.SUMMARY);
    await this.waitForPageLoad();
  }

  /**
   * Get good dreams count
   */
  async getGoodDreamsCount(): Promise<number> {
    const text = await this.helpers.getTextContent(SELECTORS.SUMMARY.GOOD_DREAMS_COUNT);
    return this.extractNumberFromText(text);
  }

  /**
   * Get bad dreams count
   */
  async getBadDreamsCount(): Promise<number> {
    const text = await this.helpers.getTextContent(SELECTORS.SUMMARY.BAD_DREAMS_COUNT);
    return this.extractNumberFromText(text);
  }

  /**
   * Get total dreams count
   */
  async getTotalDreamsCount(): Promise<number> {
    const text = await this.helpers.getTextContent(SELECTORS.SUMMARY.TOTAL_DREAMS_COUNT);
    return this.extractNumberFromText(text);
  }

  /**
   * Get recurring dreams count
   */
  async getRecurringDreamsCount(): Promise<number> {
    const text = await this.helpers.getTextContent(SELECTORS.SUMMARY.RECURRING_DREAMS_COUNT);
    return this.extractNumberFromText(text);
  }

  /**
   * Get list of recurring dreams
   */
  async getRecurringDreamsList(): Promise<string[]> {
    return await this.helpers.getAllTextContents(SELECTORS.SUMMARY.RECURRING_DREAMS_LIST);
  }

  /**
   * Extract number from text (handles formats like "Good Dreams: 6" or just "6")
   */
  private extractNumberFromText(text: string): number {
    const match = text.match(/\d+/);
    if (!match) {
      throw new Error(`Could not extract number from text: "${text}"`);
    }
    return parseInt(match[0], 10);
  }

  /**
   * Verify good dreams count
   */
  async verifyGoodDreamsCount(): Promise<void> {
    const count = await this.getGoodDreamsCount();
    expect(count).toBe(EXPECTED_DATA.SUMMARY.GOOD_DREAMS);
  }

  /**
   * Verify bad dreams count
   */
  async verifyBadDreamsCount(): Promise<void> {
    const count = await this.getBadDreamsCount();
    expect(count).toBe(EXPECTED_DATA.SUMMARY.BAD_DREAMS);
  }

  /**
   * Verify total dreams count
   */
  async verifyTotalDreamsCount(): Promise<void> {
    const count = await this.getTotalDreamsCount();
    expect(count).toBe(EXPECTED_DATA.SUMMARY.TOTAL_DREAMS);
  }

  /**
   * Verify recurring dreams count
   */
  async verifyRecurringDreamsCount(): Promise<void> {
    const count = await this.getRecurringDreamsCount();
    expect(count).toBe(EXPECTED_DATA.SUMMARY.RECURRING_DREAMS);
  }

  /**
   * Verify specific recurring dreams exist
   */
  async verifyRecurringDreamsList(): Promise<void> {
    const recurringDreams = await this.getRecurringDreamsList();
    
    // Check that we have the expected number of recurring dreams
    expect(recurringDreams.length).toBeGreaterThanOrEqual(EXPECTED_DATA.SUMMARY.RECURRING_DREAMS);
    
    // Verify specific recurring dreams are present
    for (const expectedDream of EXPECTED_DATA.SUMMARY.RECURRING_DREAM_NAMES) {
      const found = recurringDreams.some(dream => 
        dream.toLowerCase().includes(expectedDream.toLowerCase())
      );
      expect(found).toBe(true);
    }
  }

  /**
   * Get all summary statistics
   */
  async getAllSummaryStats(): Promise<{
    goodDreams: number;
    badDreams: number;
    totalDreams: number;
    recurringDreams: number;
    recurringDreamsList: string[];
  }> {
    return {
      goodDreams: await this.getGoodDreamsCount(),
      badDreams: await this.getBadDreamsCount(),
      totalDreams: await this.getTotalDreamsCount(),
      recurringDreams: await this.getRecurringDreamsCount(),
      recurringDreamsList: await this.getRecurringDreamsList()
    };
  }

  /**
   * Verify mathematical consistency (Good + Bad = Total)
   */
  async verifyMathematicalConsistency(): Promise<void> {
    const goodDreams = await this.getGoodDreamsCount();
    const badDreams = await this.getBadDreamsCount();
    const totalDreams = await this.getTotalDreamsCount();
    
    expect(goodDreams + badDreams).toBe(totalDreams);
  }

  /**
   * Check if page is loaded
   */
  async isPageLoaded(): Promise<boolean> {
    try {
      // Check if at least one of the summary elements is visible
      const goodDreamsVisible = await this.helpers.getElementByMultipleSelectors(
        SELECTORS.SUMMARY.GOOD_DREAMS_COUNT
      ).then(el => el.isVisible()).catch(() => false);
      
      const totalDreamsVisible = await this.helpers.getElementByMultipleSelectors(
        SELECTORS.SUMMARY.TOTAL_DREAMS_COUNT
      ).then(el => el.isVisible()).catch(() => false);
      
      return goodDreamsVisible || totalDreamsVisible;
    } catch (error) {
      return false;
    }
  }

  /**
   * Verify complete summary page functionality
   */
  async verifySummaryPageComplete(): Promise<void> {
    // Verify all counts match expected values
    await this.verifyGoodDreamsCount();
    await this.verifyBadDreamsCount();
    await this.verifyTotalDreamsCount();
    await this.verifyRecurringDreamsCount();
    
    // Verify specific recurring dreams
    await this.verifyRecurringDreamsList();
    
    // Verify mathematical consistency
    await this.verifyMathematicalConsistency();
  }

  /**
   * Get summary text for reporting
   */
  async getSummaryText(): Promise<string> {
    const stats = await this.getAllSummaryStats();
    return `Good Dreams: ${stats.goodDreams}, Bad Dreams: ${stats.badDreams}, ` +
           `Total: ${stats.totalDreams}, Recurring: ${stats.recurringDreams}`;
  }
}
