import { Page, Locator, expect } from '@playwright/test';
import { TIMEOUTS } from './constants';

/**
 * Helper utilities for common test operations
 */

export class TestHelpers {
  constructor(private page: Page) {}

  /**
   * Wait for element to be visible with custom timeout
   */
  async waitForElement(selector: string, timeout: number = TIMEOUTS.ELEMENT_WAIT): Promise<Locator> {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout });
    return element;
  }

  /**
   * Wait for element to disappear
   */
  async waitForElementToDisappear(selector: string, timeout: number = TIMEOUTS.ELEMENT_WAIT): Promise<void> {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'hidden', timeout });
  }

  /**
   * Get element using multiple selector strategies
   */
  async getElementByMultipleSelectors(selectors: string): Promise<Locator> {
    const selectorArray = selectors.split(', ');
    
    for (const selector of selectorArray) {
      try {
        const element = this.page.locator(selector.trim());
        await element.waitFor({ state: 'visible', timeout: 1000 });
        return element;
      } catch (error) {
        // Continue to next selector
        continue;
      }
    }
    
    throw new Error(`None of the selectors found: ${selectors}`);
  }

  /**
   * Click element with retry mechanism
   */
  async clickWithRetry(selector: string, maxRetries: number = 3): Promise<void> {
    let lastError: Error | null = null;
    
    for (let i = 0; i < maxRetries; i++) {
      try {
        const element = await this.getElementByMultipleSelectors(selector);
        await element.click();
        return;
      } catch (error) {
        lastError = error as Error;
        await this.page.waitForTimeout(500); // Wait before retry
      }
    }
    
    throw new Error(`Failed to click element after ${maxRetries} retries. Last error: ${lastError?.message}`);
  }

  /**
   * Get text content with fallback selectors
   */
  async getTextContent(selector: string): Promise<string> {
    const element = await this.getElementByMultipleSelectors(selector);
    const text = await element.textContent();
    return text?.trim() || '';
  }

  /**
   * Count elements matching selector
   */
  async countElements(selector: string): Promise<number> {
    const selectorArray = selector.split(', ');

    for (const singleSelector of selectorArray) {
      try {
        const element = this.page.locator(singleSelector.trim());
        const count = await element.count();
        if (count > 0) {
          return count;
        }
      } catch (error) {
        // Continue to next selector
        continue;
      }
    }

    return 0;
  }

  /**
   * Get all text contents from elements
   */
  async getAllTextContents(selector: string): Promise<string[]> {
    const selectorArray = selector.split(', ');

    for (const singleSelector of selectorArray) {
      try {
        const element = this.page.locator(singleSelector.trim());
        const count = await element.count();
        if (count > 0) {
          const texts = await element.allTextContents();
          return texts.map(text => text.trim()).filter(text => text.length > 0);
        }
      } catch (error) {
        // Continue to next selector
        continue;
      }
    }

    return [];
  }

  /**
   * Wait for page to load completely
   */
  async waitForPageLoad(timeout: number = TIMEOUTS.PAGE_LOAD): Promise<void> {
    await this.page.waitForLoadState('networkidle', { timeout });
  }

  /**
   * Take screenshot with timestamp
   */
  async takeScreenshot(name: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    await this.page.screenshot({ 
      path: `screenshots/${name}-${timestamp}.png`,
      fullPage: true 
    });
  }

  /**
   * Verify element contains specific text
   */
  async verifyElementText(selector: string, expectedText: string): Promise<void> {
    const element = await this.getElementByMultipleSelectors(selector);
    await expect(element).toContainText(expectedText);
  }

  /**
   * Verify element count
   */
  async verifyElementCount(selector: string, expectedCount: number): Promise<void> {
    const count = await this.countElements(selector);
    expect(count).toBe(expectedCount);
  }

  /**
   * Wait for animation to complete
   */
  async waitForAnimation(duration: number = 1000): Promise<void> {
    await this.page.waitForTimeout(duration);
  }
}
