<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dreams Diary</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #2c3e50, #3498db);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        h1::after {
            content: '📖';
            position: absolute;
            right: -60px;
            top: 0;
            font-size: 2.5rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .subtitle {
            text-align: center;
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 40px;
            font-style: italic;
        }

        .table-container {
            overflow-x: auto;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            background: white;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            background: white;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 15px;
            text-align: left;
            font-weight: 600;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            border: none;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        th:first-child {
            border-top-left-radius: 15px;
        }

        th:last-child {
            border-top-right-radius: 15px;
        }

        td {
            padding: 18px 15px;
            border-bottom: 1px solid #eee;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .dream-entry {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .dream-entry:hover {
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .dream-entry:nth-child(even) {
            background-color: #f8f9fa;
        }

        .dream-name {
            font-weight: 600;
            color: #2c3e50;
            position: relative;
        }

        .dream-name::before {
            content: '💭';
            margin-right: 8px;
            font-size: 1.2rem;
        }

        .days-ago {
            color: #666;
            font-weight: 500;
            text-align: center;
        }

        .dream-type {
            font-weight: bold;
            text-align: center;
            padding: 8px 16px;
            border-radius: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.9rem;
        }

        .good {
            background: linear-gradient(135deg, #00b894, #00cec9);
            color: white;
            box-shadow: 0 4px 15px rgba(0, 184, 148, 0.3);
        }

        .bad {
            background: linear-gradient(135deg, #e17055, #d63031);
            color: white;
            box-shadow: 0 4px 15px rgba(214, 48, 49, 0.3);
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 20px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            min-width: 150px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .search-box {
            padding: 12px 20px;
            border: 2px solid #ddd;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
            min-width: 250px;
        }

        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.2);
        }

        @media (max-width: 768px) {
            .container { padding: 20px; }
            h1 { font-size: 2rem; }
            h1::after { right: -40px; font-size: 1.8rem; }
            .stats { flex-direction: column; align-items: center; }
            .table-header { flex-direction: column; text-align: center; }
            .search-box { min-width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container fade-in">
        <h1>Dreams Diary</h1>
        <p class="subtitle">A collection of your nocturnal adventures</p>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">10</div>
                <div class="stat-label">Total Dreams</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">6</div>
                <div class="stat-label">Good Dreams</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">4</div>
                <div class="stat-label">Bad Dreams</div>
            </div>
        </div>

        <div class="table-header">
            <h2 style="color: #2c3e50; margin: 0;">Dream Entries</h2>
            <input type="text" class="search-box" placeholder="🔍 Search your dreams..." id="searchBox">
        </div>

        <div class="table-container">
            <table class="dreams-table">
        <thead>
            <tr>
                <th>Dream Name</th>
                <th>Days Ago</th>
                <th>Dream Type</th>
            </tr>
        </thead>
        <tbody>
            <tr class="dream-entry">
                <td class="dream-name">Flying over mountains</td>
                <td class="days-ago">2</td>
                <td class="dream-type good">Good</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Lost in maze</td>
                <td class="days-ago">5</td>
                <td class="dream-type bad">Bad</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Swimming with dolphins</td>
                <td class="days-ago">1</td>
                <td class="dream-type good">Good</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Falling from cliff</td>
                <td class="days-ago">7</td>
                <td class="dream-type bad">Bad</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Meeting old friends</td>
                <td class="days-ago">3</td>
                <td class="dream-type good">Good</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Being chased</td>
                <td class="days-ago">4</td>
                <td class="dream-type bad">Bad</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Winning lottery</td>
                <td class="days-ago">6</td>
                <td class="dream-type good">Good</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Exam nightmare</td>
                <td class="days-ago">8</td>
                <td class="dream-type bad">Bad</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Flying over mountains</td>
                <td class="days-ago">10</td>
                <td class="dream-type good">Good</td>
            </tr>
            <tr class="dream-entry">
                <td class="dream-name">Lost in maze</td>
                <td class="days-ago">12</td>
                <td class="dream-type good">Good</td>
            </tr>
        </tbody>
            </table>
        </div>
    </div>

    <script>
        // Search functionality
        document.getElementById('searchBox').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('.dream-entry');

            rows.forEach(row => {
                const dreamName = row.querySelector('.dream-name').textContent.toLowerCase();
                const dreamType = row.querySelector('.dream-type').textContent.toLowerCase();

                if (dreamName.includes(searchTerm) || dreamType.includes(searchTerm)) {
                    row.style.display = '';
                    row.style.animation = 'fadeIn 0.5s ease-out';
                } else {
                    row.style.display = 'none';
                }
            });
        });

        // Add hover effects and animations
        document.addEventListener('DOMContentLoaded', function() {
            const rows = document.querySelectorAll('.dream-entry');

            rows.forEach((row, index) => {
                row.style.animationDelay = (index * 0.1) + 's';
                row.classList.add('fade-in');

                // Add click effect
                row.addEventListener('click', function() {
                    this.style.transform = 'scale(1.02)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                });
            });
        });
    </script>
</body>
</html>
