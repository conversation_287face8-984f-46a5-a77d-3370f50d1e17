import { test, expect } from '@playwright/test';
import { DiaryPage } from '../src/pages/DiaryPage';
import { EXPECTED_DATA } from '../src/utils/constants';

test.describe('Diary Page Tests - dreams-diary.html', () => {
  let diaryPage: DiaryPage;

  test.beforeEach(async ({ page }) => {
    diaryPage = new DiaryPage(page);
  });

  test('should display exactly 10 dream entries', async ({ page }) => {
    test.info().annotations.push({
      type: 'requirement',
      description: 'Assert exactly 10 dream entries'
    });

    // Navigate to diary page
    await diaryPage.navigate();

    // Verify exactly 10 dream entries
    await diaryPage.verifyDreamEntriesCount();

    // Additional verification - get actual count for reporting
    const actualCount = await diaryPage.getDreamEntriesCount();
    expect(actualCount).toBe(10);

    // Take screenshot for verification
    await diaryPage.takeScreenshot('diary-page-10-entries');
  });

  test('should have valid dream types (Good or Bad)', async ({ page }) => {
    test.info().annotations.push({
      type: 'requirement',
      description: 'Assert Dream Types → Either "Good" or "Bad"'
    });

    // Navigate to diary page
    await diaryPage.navigate();

    // Verify all dream types are valid
    await diaryPage.verifyDreamTypes();

    // Get all dream types for detailed verification
    const dreamTypes = await diaryPage.getAllDreamTypes();
    
    // Verify we have dream types for all entries
    expect(dreamTypes.length).toBe(EXPECTED_DATA.DIARY.TOTAL_ENTRIES);

    // Verify each type is either "Good" or "Bad"
    for (const dreamType of dreamTypes) {
      expect(['Good', 'Bad']).toContain(dreamType);
    }

    // Log dream types for debugging
    console.log('Dream types found:', dreamTypes);
  });

  test('should have all three columns filled for each row', async ({ page }) => {
    test.info().annotations.push({
      type: 'requirement',
      description: 'Each row → 3 columns filled: Dream Name, Days Ago, Dream Type'
    });

    // Navigate to diary page
    await diaryPage.navigate();

    // Verify all columns are filled
    await diaryPage.verifyAllColumnsAreFilled();

    // Get all data for detailed verification
    const dreamNames = await diaryPage.getAllDreamNames();
    const daysAgo = await diaryPage.getAllDaysAgo();
    const dreamTypes = await diaryPage.getAllDreamTypes();

    // Verify we have data for all columns
    expect(dreamNames.length).toBe(10);
    expect(daysAgo.length).toBe(10);
    expect(dreamTypes.length).toBe(10);

    // Verify no empty values
    dreamNames.forEach((name, index) => {
      expect(name.trim()).not.toBe('');
      expect(daysAgo[index].trim()).not.toBe('');
      expect(dreamTypes[index].trim()).not.toBe('');
    });

    // Take screenshot showing all data
    await diaryPage.takeScreenshot('diary-page-all-columns-filled');
  });

  test('should display proper table structure', async ({ page }) => {
    test.info().annotations.push({
      type: 'structure',
      description: 'Verify table structure and accessibility'
    });

    // Navigate to diary page
    await diaryPage.navigate();

    // Verify table structure
    await diaryPage.verifyTableStructure();

    // Verify table is visible
    const table = await diaryPage.getDreamsTable();
    expect(await table.isVisible()).toBe(true);

    // Verify we can get rows
    const rows = await diaryPage.getDreamRows();
    expect(await rows.count()).toBe(10);
  });

  test('should provide access to individual dream entries', async ({ page }) => {
    test.info().annotations.push({
      type: 'functionality',
      description: 'Verify individual dream entry access and data integrity'
    });

    // Navigate to diary page
    await diaryPage.navigate();

    // Get all dream entries
    const allEntries = await diaryPage.getAllDreamEntries();
    expect(allEntries.length).toBe(10);

    // Verify each entry has all required fields
    for (let i = 0; i < allEntries.length; i++) {
      const entry = allEntries[i];
      
      // Verify entry structure
      expect(entry).toHaveProperty('name');
      expect(entry).toHaveProperty('daysAgo');
      expect(entry).toHaveProperty('type');

      // Verify values are not empty
      expect(entry.name.trim()).not.toBe('');
      expect(entry.daysAgo.trim()).not.toBe('');
      expect(entry.type.trim()).not.toBe('');

      // Verify dream type is valid
      expect(['Good', 'Bad']).toContain(entry.type);

      // Test individual entry access
      const individualEntry = await diaryPage.getDreamEntryByIndex(i);
      expect(individualEntry).toEqual(entry);
    }

    // Log first few entries for debugging
    console.log('First 3 dream entries:', allEntries.slice(0, 3));
  });

  test('should complete full diary page verification', async ({ page }) => {
    test.info().annotations.push({
      type: 'comprehensive',
      description: 'Complete verification of all diary page requirements'
    });

    // Navigate to diary page
    await diaryPage.navigate();

    // Run complete verification
    await diaryPage.verifyDiaryPageComplete();

    // Additional verification - check page is properly loaded
    const isLoaded = await diaryPage.isPageLoaded();
    expect(isLoaded).toBe(true);

    // Take final screenshot
    await diaryPage.takeScreenshot('diary-page-complete-verification');
  });

  test('should handle page reload correctly', async ({ page }) => {
    test.info().annotations.push({
      type: 'robustness',
      description: 'Verify diary page works correctly after reload'
    });

    // Navigate to diary page
    await diaryPage.navigate();

    // Verify initial state
    await diaryPage.verifyDreamEntriesCount();

    // Reload the page
    await page.reload();

    // Verify everything still works after reload
    await diaryPage.verifyDiaryPageComplete();

    // Verify count is still correct
    const count = await diaryPage.getDreamEntriesCount();
    expect(count).toBe(10);
  });
});
