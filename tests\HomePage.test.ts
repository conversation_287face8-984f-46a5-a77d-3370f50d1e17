import { test, expect, Page } from '@playwright/test';
import { HomePage } from '../src/pages/HomePage';
import { DiaryPage } from '../src/pages/DiaryPage';
import { SummaryPage } from '../src/pages/SummaryPage';

test.describe('Home Page Tests - index.html', () => {
  let homePage: HomePage;

  test.beforeEach(async ({ page }) => {
    homePage = new HomePage(page);
  });

  test('should display loading animation and then My Dreams button', async ({ page }) => {
    test.info().annotations.push({
      type: 'requirement',
      description: 'Verify loading animation appears, then disappears (~3 sec)'
    });

    // Navigate to home page
    await homePage.navigate();

    // Verify loading sequence: animation appears, disappears, button appears
    await homePage.verifyLoadingSequence();

    // Take screenshot for verification
    await homePage.takeScreenshot('home-page-loaded');
  });

  test('should show My Dreams button after loading', async ({ page }) => {
    test.info().annotations.push({
      type: 'requirement',
      description: 'Check that "My Dreams" button appears'
    });

    // Navigate to home page
    await homePage.navigate();

    // Wait for loading to complete
    await homePage.waitForLoadingAnimationToDisappear();

    // Verify My Dreams button is visible
    const isButtonVisible = await homePage.isMyDreamsButtonVisible();
    expect(isButtonVisible).toBe(true);

    // Verify button text contains "My Dreams"
    const buttonText = await homePage.getMyDreamsButtonText();
    expect(buttonText.toLowerCase()).toContain('my dreams');
  });

  test('should open two new tabs when My Dreams button is clicked', async ({ page, context }) => {
    test.info().annotations.push({
      type: 'requirement',
      description: 'Click "My Dreams" → Assert two new tabs/windows open (Diary + Summary)'
    });

    // Navigate to home page
    await homePage.navigate();

    // Wait for page to be ready
    await homePage.waitForLoadingAnimationToDisappear();
    await homePage.waitForMyDreamsButton();

    // Get initial page count
    const initialPageCount = context.pages().length;
    expect(initialPageCount).toBe(1); // Should start with 1 page

    // Click My Dreams button and wait for new pages
    const allPages = await homePage.clickMyDreamsAndWaitForNewPages();

    // Verify we now have 3 pages total (original + 2 new)
    expect(allPages.length).toBe(3);

    // Verify the new pages are the diary and summary pages
    const pageUrls = allPages.map(p => p.url());
    
    // Check that we have pages that look like diary and summary
    // Note: Update these URL checks based on your actual file paths
    const hasDiaryPage = pageUrls.some(url => 
      url.includes('dreams-diary') || url.includes('diary')
    );
    const hasSummaryPage = pageUrls.some(url => 
      url.includes('dreams-total') || url.includes('summary') || url.includes('total')
    );

    // If URL checking doesn't work (for file:// URLs), verify by page content
    if (!hasDiaryPage || !hasSummaryPage) {
      // Alternative verification by checking page content
      let diaryPageFound = false;
      let summaryPageFound = false;

      for (const testPage of allPages) {
        if (testPage !== page) { // Skip the original home page
          try {
            const diaryPage = new DiaryPage(testPage);
            if (await diaryPage.isPageLoaded()) {
              diaryPageFound = true;
              continue;
            }
          } catch (error) {
            // Not a diary page
          }

          try {
            const summaryPage = new SummaryPage(testPage);
            if (await summaryPage.isPageLoaded()) {
              summaryPageFound = true;
            }
          } catch (error) {
            // Not a summary page
          }
        }
      }

      expect(diaryPageFound).toBe(true);
      expect(summaryPageFound).toBe(true);
    }

    // Take screenshot of all open pages
    await homePage.takeScreenshot('home-page-after-click');
  });

  test('should maintain home page functionality after navigation', async ({ page }) => {
    test.info().annotations.push({
      type: 'additional',
      description: 'Verify home page remains functional after button click'
    });

    // Navigate to home page
    await homePage.navigate();

    // Complete the loading sequence
    await homePage.verifyLoadingSequence();

    // Verify page is still loaded and functional
    const isPageLoaded = await homePage.isPageLoaded();
    expect(isPageLoaded).toBe(true);

    // Verify we can still interact with the button
    const isButtonVisible = await homePage.isMyDreamsButtonVisible();
    expect(isButtonVisible).toBe(true);
  });

  test('should handle page reload correctly', async ({ page }) => {
    test.info().annotations.push({
      type: 'robustness',
      description: 'Verify home page works correctly after reload'
    });

    // Navigate to home page
    await homePage.navigate();

    // Wait for initial load
    await homePage.verifyLoadingSequence();

    // Reload the page
    await page.reload();

    // Verify loading sequence works again after reload
    await homePage.verifyLoadingSequence();

    // Verify button is still functional
    const isButtonVisible = await homePage.isMyDreamsButtonVisible();
    expect(isButtonVisible).toBe(true);
  });
});
