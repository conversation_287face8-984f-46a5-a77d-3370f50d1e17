<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dreams Summary</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .summary-card { 
            background: #f9f9f9; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 20px; 
            margin: 10px 0; 
            display: inline-block; 
            min-width: 200px;
        }
        .count { font-size: 24px; font-weight: bold; color: #333; }
        .label { font-size: 14px; color: #666; }
        .good { color: green; }
        .bad { color: red; }
        .recurring-list { margin: 20px 0; }
        .recurring-list li { margin: 5px 0; }
    </style>
</head>
<body>
    <h1>Dreams Summary</h1>
    
    <div class="summary-container">
        <div class="summary-card">
            <div class="count good-dreams" id="goodDreams" data-testid="good-dreams">6</div>
            <div class="label">Good Dreams</div>
        </div>

        <div class="summary-card">
            <div class="count bad-dreams" id="badDreams" data-testid="bad-dreams">4</div>
            <div class="label">Bad Dreams</div>
        </div>

        <div class="summary-card">
            <div class="count total-dreams" id="totalDreams" data-testid="total-dreams">10</div>
            <div class="label">Total Dreams</div>
        </div>

        <div class="summary-card">
            <div class="count recurring-dreams" id="recurringDreams" data-testid="recurring-dreams">2</div>
            <div class="label">Recurring Dreams</div>
        </div>
    </div>
    
    <h2>Recurring Dreams</h2>
    <ul class="recurring-list" id="recurringList" data-testid="recurring-list">
        <li>Flying over mountains</li>
        <li>Lost in maze</li>
    </ul>
</body>
</html>
