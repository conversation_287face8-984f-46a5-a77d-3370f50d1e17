<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dreams Summary</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 50px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            text-align: center;
            font-size: 3.5rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #8360c3, #2ebf91);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        h1::before {
            content: '📊';
            position: absolute;
            left: -80px;
            top: 0;
            font-size: 3rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .subtitle {
            text-align: center;
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 50px;
            font-style: italic;
        }

        .summary-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            color: white;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .summary-card:hover::before {
            opacity: 1;
            transform: rotate(45deg) translate(50%, 50%);
        }

        .summary-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
        }

        .summary-card.good-card {
            background: linear-gradient(135deg, #00b894, #00cec9);
            box-shadow: 0 15px 35px rgba(0, 184, 148, 0.3);
        }

        .summary-card.good-card:hover {
            box-shadow: 0 25px 50px rgba(0, 184, 148, 0.4);
        }

        .summary-card.bad-card {
            background: linear-gradient(135deg, #e17055, #d63031);
            box-shadow: 0 15px 35px rgba(214, 48, 49, 0.3);
        }

        .summary-card.bad-card:hover {
            box-shadow: 0 25px 50px rgba(214, 48, 49, 0.4);
        }

        .summary-card.total-card {
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            box-shadow: 0 15px 35px rgba(108, 92, 231, 0.3);
        }

        .summary-card.total-card:hover {
            box-shadow: 0 25px 50px rgba(108, 92, 231, 0.4);
        }

        .summary-card.recurring-card {
            background: linear-gradient(135deg, #fd79a8, #fdcb6e);
            box-shadow: 0 15px 35px rgba(253, 121, 168, 0.3);
        }

        .summary-card.recurring-card:hover {
            box-shadow: 0 25px 50px rgba(253, 121, 168, 0.4);
        }

        .count {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            animation: countUp 1s ease-out;
        }

        @keyframes countUp {
            from { transform: scale(0); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        .label {
            font-size: 1.1rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
            opacity: 0.9;
        }

        .icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            display: block;
        }

        .recurring-section {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            padding: 40px;
            margin-top: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .recurring-section h2 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 25px;
            text-align: center;
            position: relative;
        }

        .recurring-section h2::after {
            content: '🔄';
            position: absolute;
            right: -50px;
            top: 0;
            font-size: 2rem;
            animation: rotate 3s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .recurring-list {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .recurring-list li {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            position: relative;
            font-size: 1.2rem;
            font-weight: 500;
            color: #2c3e50;
        }

        .recurring-list li::before {
            content: '💭';
            position: absolute;
            left: -15px;
            top: 50%;
            transform: translateY(-50%);
            background: white;
            padding: 5px;
            border-radius: 50%;
            font-size: 1.5rem;
        }

        .recurring-list li:hover {
            transform: translateX(10px);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
            border-left-color: #2ebf91;
        }

        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .chart-container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .chart {
            display: flex;
            justify-content: center;
            align-items: end;
            height: 200px;
            gap: 20px;
            margin: 20px 0;
        }

        .bar {
            width: 60px;
            background: linear-gradient(to top, #667eea, #764ba2);
            border-radius: 10px 10px 0 0;
            position: relative;
            transition: all 0.3s ease;
            animation: growUp 1.5s ease-out;
        }

        @keyframes growUp {
            from { height: 0; }
        }

        .bar.good-bar {
            height: 120px;
            background: linear-gradient(to top, #00b894, #00cec9);
        }

        .bar.bad-bar {
            height: 80px;
            background: linear-gradient(to top, #e17055, #d63031);
        }

        .bar:hover {
            transform: scale(1.1);
        }

        .bar-label {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: 600;
            color: #2c3e50;
        }

        .bar-value {
            position: absolute;
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-weight: bold;
            color: #2c3e50;
            background: white;
            padding: 5px 10px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
            .container { padding: 30px 20px; }
            h1 { font-size: 2.5rem; }
            h1::before { left: -60px; font-size: 2rem; }
            .summary-container { grid-template-columns: 1fr; }
            .count { font-size: 3rem; }
            .recurring-list { grid-template-columns: 1fr; }
            .chart { flex-direction: column; height: auto; align-items: center; }
            .bar { width: 200px; height: 40px !important; }
            .bar-label { position: static; margin-top: 10px; }
            .bar-value { position: static; margin-bottom: 10px; }
        }
    </style>
</head>
<body>
    <div class="container fade-in">
        <h1>Dreams Summary</h1>
        <p class="subtitle">Your dream analytics and insights</p>

        <div class="summary-container">
            <div class="summary-card good-card">
                <span class="icon">😊</span>
                <div class="count good-dreams" id="goodDreams">6</div>
                <div class="label">Good Dreams</div>
            </div>

            <div class="summary-card bad-card">
                <span class="icon">😰</span>
                <div class="count bad-dreams" id="badDreams">4</div>
                <div class="label">Bad Dreams</div>
            </div>

            <div class="summary-card total-card">
                <span class="icon">🌙</span>
                <div class="count total-dreams" id="totalDreams">10</div>
                <div class="label">Total Dreams</div>
            </div>

            <div class="summary-card recurring-card">
                <span class="icon">🔄</span>
                <div class="count recurring-dreams" id="recurringDreams">2</div>
                <div class="label">Recurring Dreams</div>
            </div>
        </div>

        <div class="chart-container">
            <h3 style="color: #2c3e50; margin-bottom: 20px; font-size: 1.8rem;">Dream Distribution</h3>
            <div class="chart">
                <div class="bar good-bar">
                    <div class="bar-value">6</div>
                    <div class="bar-label">Good</div>
                </div>
                <div class="bar bad-bar">
                    <div class="bar-value">4</div>
                    <div class="bar-label">Bad</div>
                </div>
            </div>
        </div>

        <div class="recurring-section">
            <h2>Recurring Dreams</h2>
            <ul class="recurring-list" id="recurringList">
                <li>Flying over mountains</li>
                <li>Lost in maze</li>
            </ul>
        </div>
    </div>

    <script>
        // Animate counters
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 50;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current);
            }, 30);
        }

        // Initialize animations when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Animate counters
            setTimeout(() => {
                animateCounter(document.getElementById('goodDreams'), 6);
                animateCounter(document.getElementById('badDreams'), 4);
                animateCounter(document.getElementById('totalDreams'), 10);
                animateCounter(document.getElementById('recurringDreams'), 2);
            }, 500);

            // Add staggered animation to cards
            const cards = document.querySelectorAll('.summary-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = (index * 0.2) + 's';
                card.classList.add('fade-in');
            });

            // Add staggered animation to recurring dreams
            const recurringItems = document.querySelectorAll('.recurring-list li');
            recurringItems.forEach((item, index) => {
                item.style.animationDelay = (index * 0.3 + 1) + 's';
                item.classList.add('fade-in');
            });

            // Add hover sound effect simulation
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-10px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                });
            });
        });
    </script>
</body>
</html>
