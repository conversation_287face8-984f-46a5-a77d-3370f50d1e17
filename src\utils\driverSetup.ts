import { <PERSON><PERSON><PERSON>, <PERSON>rows<PERSON><PERSON><PERSON>x<PERSON>, Page, chromium, firefox, webkit } from '@playwright/test';
import { TEST_DATA } from './constants';

/**
 * Browser setup utilities for Playwright tests
 */

export type BrowserType = 'chromium' | 'firefox' | 'webkit';

export class BrowserSetup {
  private browser: Browser | null = null;
  private context: BrowserContext | null = null;
  private page: Page | null = null;

  /**
   * Launch browser and create context
   */
  async launchBrowser(browserType: BrowserType = 'chromium', headless: boolean = true): Promise<void> {
    const browserEngine = this.getBrowserEngine(browserType);
    
    this.browser = await browserEngine.launch({
      headless,
      slowMo: 100 // Add slight delay for better visibility in headed mode
    });

    this.context = await this.browser.newContext({
      viewport: TEST_DATA.BROWSER_CONTEXTS.VIEWPORT,
      userAgent: TEST_DATA.BROWSER_CONTEXTS.USER_AGENT
    });

    this.page = await this.context.newPage();
  }

  /**
   * Get browser engine based on type
   */
  private getBrowserEngine(browserType: BrowserType) {
    switch (browserType) {
      case 'firefox':
        return firefox;
      case 'webkit':
        return webkit;
      case 'chromium':
      default:
        return chromium;
    }
  }

  /**
   * Get current page instance
   */
  getPage(): Page {
    if (!this.page) {
      throw new Error('Browser not launched. Call launchBrowser() first.');
    }
    return this.page;
  }

  /**
   * Get current context instance
   */
  getContext(): BrowserContext {
    if (!this.context) {
      throw new Error('Browser context not created. Call launchBrowser() first.');
    }
    return this.context;
  }

  /**
   * Get current browser instance
   */
  getBrowser(): Browser {
    if (!this.browser) {
      throw new Error('Browser not launched. Call launchBrowser() first.');
    }
    return this.browser;
  }

  /**
   * Close browser and cleanup
   */
  async closeBrowser(): Promise<void> {
    if (this.page) {
      await this.page.close();
      this.page = null;
    }
    
    if (this.context) {
      await this.context.close();
      this.context = null;
    }
    
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Create new page in existing context
   */
  async createNewPage(): Promise<Page> {
    if (!this.context) {
      throw new Error('Browser context not available. Call launchBrowser() first.');
    }
    return await this.context.newPage();
  }

  /**
   * Wait for multiple pages to be opened (useful for testing new tabs/windows)
   */
  async waitForNewPages(expectedCount: number, timeout: number = 10000): Promise<Page[]> {
    if (!this.context) {
      throw new Error('Browser context not available. Call launchBrowser() first.');
    }

    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        reject(new Error(`Timeout waiting for ${expectedCount} pages. Current count: ${this.context!.pages().length}`));
      }, timeout);

      const checkPages = () => {
        const pages = this.context!.pages();
        if (pages.length >= expectedCount) {
          clearTimeout(timeoutId);
          resolve(pages);
        } else {
          setTimeout(checkPages, 100);
        }
      };

      checkPages();
    });
  }
}
